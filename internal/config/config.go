package config

import (
	"encoding/json"
	"os"
	"path/filepath"
	"sync/atomic"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

type Config struct {
	Qdrant struct {
		Host           string `json:"host" mapstructure:"host"`                       // Qdrant服务器地址
		Port           int    `json:"port" mapstructure:"port"`                       // Qdrant服务器端口
		CollectionName string `json:"collection_name" mapstructure:"collection_name"` // 向量集合名称
		VectorSize     int    `json:"vector_size" mapstructure:"vector_size"`         // 向量维度
	} `json:"qdrant" mapstructure:"qdrant"`

	Dashscope struct {
		ApiKey            string `json:"api_key" mapstructure:"api_key"`                       // 通义千问API密钥
		EmbeddingEndpoint string `json:"embedding_endpoint" mapstructure:"embedding_endpoint"` // 文本嵌入API端点
		LlmEndpoint       string `json:"llm_endpoint" mapstructure:"llm_endpoint"`             // 大语言模型API端点
	} `json:"dashscope" mapstructure:"dashscope"`

	Email struct {
		SmtpHost      string `json:"smtp_host" mapstructure:"smtp_host"`           // SMTP服务器地址
		SmtpPort      int    `json:"smtp_port" mapstructure:"smtp_port"`           // SMTP服务器端口
		SmtpUser      string `json:"smtp_user" mapstructure:"smtp_user"`           // SMTP用户名
		SmtpPassword  string `json:"smtp_password" mapstructure:"smtp_password"`   // SMTP密码
		EvernoteEmail string `json:"evernote_email" mapstructure:"evernote_email"` // 印象笔记邮箱
		AdminEmail    string `json:"admin_email" mapstructure:"admin_email"`       // 管理员邮箱
	} `json:"email" mapstructure:"email"`

	Hotlist struct {
		CrawlInterval          string `json:"crawl_interval" mapstructure:"crawl_interval"`                     // 热榜爬取间隔
		TopicAggregationLimit  int    `json:"topic_aggregation_limit" mapstructure:"topic_aggregation_limit"`   // 话题聚合数量限制
		ArticleGenerationLimit int    `json:"article_generation_limit" mapstructure:"article_generation_limit"` // 文章生成数量限制
	} `json:"hotlist" mapstructure:"hotlist"`

	Crawler struct {
		CrawlInterval string   `json:"crawl_interval" mapstructure:"crawl_interval"` // 新闻爬取间隔
		ProxyList     []string `json:"proxy_list" mapstructure:"proxy_list"`         // 代理服务器列表
	} `json:"crawler" mapstructure:"crawler"`

	Vector struct {
		BatchSize   int `json:"batch_size" mapstructure:"batch_size"`     // 批处理大小
		MaxRetries  int `json:"max_retries" mapstructure:"max_retries"`   // 最大重试次数
		CleanupDays int `json:"cleanup_days" mapstructure:"cleanup_days"` // 数据清理天数
	} `json:"vector" mapstructure:"vector"`

	Log struct {
		Level string `json:"level" mapstructure:"level"` // 日志级别 (debug/info/warn/error/fatal)
		File  string `json:"file" mapstructure:"file"`   // 日志文件路径
	} `json:"log" mapstructure:"log"`
}

var (
	globalConfig atomic.Value
	configPath   string
)

func initConfig(path string) error {
	configPath = path // 保存配置文件路径
	v := viper.New()
	v.SetConfigFile(path) // 自动识别扩展名 (.json / .yaml ...)
	v.AutomaticEnv()      // 环境变量覆盖，同名用下划线 LOG_LEVEL
	if err := v.ReadInConfig(); err != nil {
		return err
	}
	var c Config
	if err := v.Unmarshal(&c); err != nil {
		return err
	} else {
		globalConfig.Store(&c) // 将加载的配置存入全局变量
	}

	// 热更新（可选）
	v.WatchConfig()
	v.OnConfigChange(func(_ fsnotify.Event) {
		var c Config
		if err := viper.Unmarshal(&c); err == nil {
			globalConfig.Store(&c)
		}
	})

	return nil
}
func GetConfig() *Config {
	if cfg := globalConfig.Load(); cfg != nil {
		return cfg.(*Config)
	}
	return nil
}

// SaveConfig 保存配置到文件
func SaveConfig(cfg *Config) error {
	if configPath == "" {
		return os.ErrInvalid
	}

	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return err
	}

	// 将配置结构体转换为JSON格式
	jsonData, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	if err := os.WriteFile(configPath, jsonData, 0644); err != nil {
		return err
	}

	// 更新内存中的配置
	globalConfig.Store(cfg)

	return nil
}

func init() {
	if err := initConfig("./etc/config.json"); err != nil {
		panic("Failed to load config: " + err.Error())
	}
}
