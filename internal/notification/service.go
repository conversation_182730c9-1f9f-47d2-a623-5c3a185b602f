package notification

import (
	"fmt"
	"log"
	"net/smtp"
	"strconv"
	"strings"
	"time"

	"newsBot/internal/config"
	"newsBot/internal/core"
)

// EmailNotifier 邮件通知服务
type EmailNotifier struct {
	SMTPHost      string
	SMTPPort      string
	SMTPUser      string
	SMTPPassword  string
	EvernoteEmail string
}

// NewEmailNotifier 创建新的邮件通知服务
func NewEmailNotifier() *EmailNotifier {
	cfg := config.GetConfig()
	if cfg == nil {
		panic("配置加载失败")
	}

	return &EmailNotifier{
		SMTPHost:      cfg.Email.SmtpHost,
		SMTPPort:      strconv.Itoa(cfg.Email.SmtpPort),
		SMTPUser:      cfg.Email.SmtpUser,
		SMTPPassword:  cfg.Email.SmtpPassword,
		EvernoteEmail: cfg.Email.EvernoteEmail,
	}
}

// SendToEvernote 发送文章到印象笔记
func (e *EmailNotifier) SendToEvernote(articles []core.Article) error {
	if e.EvernoteEmail == "" {
		log.Printf("⚠️ 印象笔记邮箱地址未配置，跳过发送")
		return fmt.Errorf("印象笔记邮箱地址未配置")
	}

	log.Printf("📧 准备发送 %d 篇文章到印象笔记 (%s)...", len(articles), e.EvernoteEmail)

	successCount := 0
	for i, article := range articles {
		log.Printf("发送文章 %d/%d: %s", i+1, len(articles), article.Title)

		subject := fmt.Sprintf("📰 %s", article.Title)
		content := e.formatArticleForEvernote(article)

		if err := e.SendEmail(subject, content, []string{e.EvernoteEmail}); err != nil {
			log.Printf("❌ 文章 '%s' 发送失败: %v", article.Title, err)
			// 不立即返回错误，继续尝试发送其他文章
			continue
		}

		log.Printf("✅ 文章 '%s' 发送成功", article.Title)
		successCount++

		// 添加延迟避免邮件服务器限制
		if i < len(articles)-1 {
			time.Sleep(2 * time.Second)
		}
	}

	if successCount == 0 {
		return fmt.Errorf("所有文章发送失败")
	}

	log.Printf("🎉 成功发送 %d/%d 篇文章到印象笔记", successCount, len(articles))
	return nil
}

// SendEmail 发送邮件
func (e *EmailNotifier) SendEmail(subject, content string, recipients []string) error {
	if e.SMTPUser == "" || e.SMTPPassword == "" {
		return fmt.Errorf("SMTP配置不完整")
	}

	// 构建邮件内容
	message := e.buildEmailMessage(e.SMTPUser, recipients, subject, content)

	// SMTP认证
	auth := smtp.PlainAuth("", e.SMTPUser, e.SMTPPassword, e.SMTPHost)

	// 发送邮件
	addr := e.SMTPHost + ":" + e.SMTPPort
	err := smtp.SendMail(addr, auth, e.SMTPUser, recipients, []byte(message))
	if err != nil {
		// 检查是否是QQ邮箱的"假错误"
		errStr := err.Error()
		if strings.Contains(errStr, "short response") {
			log.Printf("⚠️ SMTP响应异常但可能发送成功: %v", err)
			log.Printf("💡 请检查收件箱确认邮件是否实际发送成功")
			// 不返回错误，因为邮件可能实际发送成功了
			return nil
		}
		return fmt.Errorf("邮件发送失败: %v", err)
	}

	return nil
}

// SendSummaryReport 发送汇总报告
func (e *EmailNotifier) SendSummaryReport(result core.WorkflowResult) error {
	subject := fmt.Sprintf("📊 每日内容生成报告 - %s", result.ExecutedAt.Format("2006-01-02"))

	content := e.formatSummaryReport(result)

	// 发送给管理员
	cfg := config.GetConfig()
	recipients := []string{e.SMTPUser}
	if cfg != nil && cfg.Email.AdminEmail != "" {
		recipients = []string{cfg.Email.AdminEmail}
	}

	return e.SendEmail(subject, content, recipients)
}

// formatArticleForEvernote 格式化文章用于印象笔记
func (e *EmailNotifier) formatArticleForEvernote(article core.Article) string {
	var content strings.Builder

	// 添加HTML格式
	content.WriteString("<!DOCTYPE html>\n")
	content.WriteString("<html>\n<head>\n")
	content.WriteString("<meta charset=\"UTF-8\">\n")
	content.WriteString(fmt.Sprintf("<title>%s</title>\n", article.Title))
	content.WriteString("<style>\n")
	content.WriteString(`
		body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
		.header { border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
		.title { color: #333; font-size: 24px; margin-bottom: 10px; }
		.meta { color: #666; font-size: 14px; margin-bottom: 20px; }
		.summary { background-color: #f5f5f5; padding: 15px; border-left: 4px solid #007acc; margin-bottom: 20px; }
		.content { font-size: 16px; line-height: 1.8; }
		.footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px; }
	`)
	content.WriteString("</style>\n</head>\n<body>\n")

	// 文章头部
	content.WriteString("<div class=\"header\">\n")
	content.WriteString(fmt.Sprintf("<h1 class=\"title\">%s</h1>\n", article.Title))
	content.WriteString(fmt.Sprintf("<div class=\"meta\">生成时间: %s | 字数: %d</div>\n",
		article.CreatedAt.Format("2006-01-02 15:04:05"), article.WordCount))
	content.WriteString("</div>\n")

	// 文章摘要
	if article.Summary != "" {
		content.WriteString("<div class=\"summary\">\n")
		content.WriteString("<strong>摘要：</strong><br>\n")
		content.WriteString(article.Summary)
		content.WriteString("\n</div>\n")
	}

	// 文章正文
	content.WriteString("<div class=\"content\">\n")
	// 将换行符转换为HTML换行
	htmlContent := strings.ReplaceAll(article.Content, "\n", "<br>\n")
	content.WriteString(htmlContent)
	content.WriteString("\n</div>\n")

	// 页脚
	content.WriteString("<div class=\"footer\">\n")
	content.WriteString("本文由AI自动生成，仅供参考。<br>\n")
	content.WriteString(fmt.Sprintf("话题ID: %s<br>\n", article.TopicId))
	content.WriteString(fmt.Sprintf("生成时间: %s\n", article.CreatedAt.Format("2006-01-02 15:04:05")))
	content.WriteString("</div>\n")

	content.WriteString("</body>\n</html>")

	return content.String()
}

// formatSummaryReport 格式化汇总报告
func (e *EmailNotifier) formatSummaryReport(result core.WorkflowResult) string {
	var content strings.Builder

	content.WriteString("<!DOCTYPE html>\n<html>\n<head>\n<meta charset=\"UTF-8\">\n")
	content.WriteString("<title>每日内容生成报告</title>\n")
	content.WriteString("<style>body{font-family:Arial,sans-serif;margin:20px;}.status{padding:10px;margin:10px 0;border-radius:5px;}.success{background-color:#d4edda;color:#155724;}.error{background-color:#f8d7da;color:#721c24;}.info{background-color:#d1ecf1;color:#0c5460;}</style>\n")
	content.WriteString("</head>\n<body>\n")

	content.WriteString("<h1>📊 每日内容生成报告</h1>\n")

	// 执行状态
	statusClass := "success"
	statusText := "✅ 执行成功"
	if !result.Success {
		statusClass = "error"
		statusText = "❌ 执行失败"
	}

	content.WriteString(fmt.Sprintf("<div class=\"status %s\">%s</div>\n", statusClass, statusText))

	// 统计信息
	content.WriteString("<h2>📈 统计信息</h2>\n")
	content.WriteString("<ul>\n")
	content.WriteString(fmt.Sprintf("<li><strong>执行时间：</strong>%s</li>\n", result.ExecutedAt.Format("2006-01-02 15:04:05")))
	content.WriteString(fmt.Sprintf("<li><strong>耗时：</strong>%s</li>\n", result.Duration))
	content.WriteString(fmt.Sprintf("<li><strong>聚合话题数：</strong>%d</li>\n", result.TopicsCount))
	content.WriteString(fmt.Sprintf("<li><strong>生成文章数：</strong>%d</li>\n", result.ArticlesCount))
	content.WriteString("</ul>\n")

	// 错误信息
	if result.Error != "" {
		content.WriteString("<h2>❌ 错误信息</h2>\n")
		content.WriteString(fmt.Sprintf("<div class=\"status error\">%s</div>\n", result.Error))
	}

	// 系统信息
	content.WriteString("<h2>🔧 系统信息</h2>\n")
	content.WriteString("<ul>\n")
	content.WriteString(fmt.Sprintf("<li><strong>报告生成时间：</strong>%s</li>\n", time.Now().Format("2006-01-02 15:04:05")))
	content.WriteString("<li><strong>系统版本：</strong>v1.0.0</li>\n")
	content.WriteString("</ul>\n")

	content.WriteString("</body>\n</html>")

	return content.String()
}

// buildEmailMessage 构建邮件消息
func (e *EmailNotifier) buildEmailMessage(from string, to []string, subject, body string) string {
	var message strings.Builder

	message.WriteString(fmt.Sprintf("From: %s\r\n", from))
	message.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(to, ",")))
	message.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	message.WriteString("MIME-Version: 1.0\r\n")
	message.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
	message.WriteString("\r\n")
	message.WriteString(body)

	return message.String()
}
