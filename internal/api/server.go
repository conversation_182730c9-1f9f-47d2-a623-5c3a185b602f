package api

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"newsBot/internal/models"
	"strconv"
	"strings"
	"time"

	"newsBot/internal/model"
	"newsBot/internal/svc"
	"newsBot/internal/vector"
)

// Server HTTP API服务器
type Server struct {
	vectorService *vector.HTTPService
	port          int
}

// NewServer 创建新的API服务器
func NewServer(port int) (*Server, error) {
	// 尝试初始化向量服务，失败时继续运行
	vectorService, err := vector.NewHTTPService()
	if err != nil {
		log.Printf("向量服务初始化失败: %v", err)
		log.Println("将在没有向量服务的情况下运行")
		vectorService = nil
	}

	return &Server{
		vectorService: vectorService,
		port:          port,
	}, nil
}

// Start 启动服务器
func (s *Server) Start() error {
	// 设置路由
	http.HandleFunc("/health", s.healthHandler)
	http.HandleFunc("/search", s.searchHandler)
	http.HandleFunc("/stats", s.statsHandler)
	http.HandleFunc("/similar", s.similarHandler)

	// 新增质量检测相关接口
	http.HandleFunc("/quality/sessions", s.qualitySessionsHandler)
	http.HandleFunc("/quality/news", s.qualityNewsHandler)
	http.HandleFunc("/quality/stats", s.qualityStatsHandler)
	http.HandleFunc("/quality/detail", s.qualityDetailHandler)
	http.HandleFunc("/article/", s.articleDetailHandler)

	// 热搜相关接口
	http.HandleFunc("/hotlist/sessions", s.hotlistSessionsHandler)
	http.HandleFunc("/hotlist/records", s.hotlistRecordsHandler)
	http.HandleFunc("/hotlist/stats", s.hotlistStatsHandler)

	// LLM处理留痕接口
	http.HandleFunc("/llm/topic-aggregation", s.topicAggregationRecordsHandler)
	http.HandleFunc("/llm/vector-search", s.vectorSearchRecordsHandler)
	http.HandleFunc("/llm/generated-articles", s.generatedArticleRecordsHandler)

	// 新增：按会话获取数据的接口
	http.HandleFunc("/llm/topic-aggregation/session", s.topicAggregationBySessionHandler)
	http.HandleFunc("/llm/vector-search/topic", s.vectorSearchByTopicHandler)
	http.HandleFunc("/llm/generated-articles/topic", s.generatedArticlesByTopicHandler)
	http.HandleFunc("/search/results", s.searchResultsHandler)
	http.HandleFunc("/llm/generated-articles/detail", s.generatedArticleDetailHandler)

	http.HandleFunc("/test", s.testHandler)

	// 静态文件服务
	http.HandleFunc("/", s.indexHandler)

	// 启动服务器
	addr := fmt.Sprintf(":%d", s.port)
	log.Printf("API服务器启动在端口 %d", s.port)
	log.Printf("访问 http://localhost:%d 查看质量检测界面", s.port)

	return http.ListenAndServe(addr, nil)
}

// Close 关闭服务器
func (s *Server) Close() error {
	if s.vectorService != nil {
		s.vectorService.Close()
	}
	return nil
}

// healthHandler 健康检查处理器
func (s *Server) healthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := map[string]interface{}{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
	}

	// 检查向量服务健康状态
	if s.vectorService != nil {
		if err := s.vectorService.HealthCheck(); err != nil {
			response["vector_service"] = "error: " + err.Error()
		} else {
			response["vector_service"] = "ok"
		}
	} else {
		response["vector_service"] = "disabled"
	}

	// 检查数据库状态
	svcCtx := svc.SvcCtx
	if svcCtx != nil && svcCtx.DB != nil {
		response["database"] = "ok"
	} else {
		response["database"] = "disabled"
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// indexHandler 主页处理器
func (s *Server) indexHandler(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	// 读取HTML文件
	htmlBytes, err := ioutil.ReadFile("web/index.html")
	if err != nil {
		log.Printf("读取HTML文件失败: %v", err)
		// 返回简单的错误页面
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Write([]byte(`
			<html>
			<head><title>新闻爬虫质量检测系统</title></head>
			<body>
				<h1>新闻爬虫质量检测系统</h1>
				<p>HTML文件加载失败，请检查 web/index.html 文件是否存在。</p>
				<p><a href="/test">访问测试页面</a></p>
			</body>
			</html>
		`))
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write(htmlBytes)
}

// testHandler 测试页面处理器
func (s *Server) testHandler(w http.ResponseWriter, r *http.Request) {
	html := `<!DOCTYPE html>
<html>
<head>
    <title>测试页面</title>
</head>
<body>
    <h1>API测试</h1>
    <div id="stats"></div>
    <div id="sessions"></div>
    <script>
        console.log('页面加载完成');
        
        fetch('/quality/stats')
            .then(response => response.json())
            .then(data => {
                console.log('统计数据:', data);
                document.getElementById('stats').innerHTML = '<h2>统计数据</h2><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('统计数据加载失败:', error);
                document.getElementById('stats').innerHTML = '<h2>统计数据加载失败</h2>';
            });
            
        fetch('/quality/sessions?limit=5')
            .then(response => response.json())
            .then(data => {
                console.log('会话数据:', data);
                document.getElementById('sessions').innerHTML = '<h2>会话数据</h2><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('会话数据加载失败:', error);
                document.getElementById('sessions').innerHTML = '<h2>会话数据加载失败</h2>';
            });
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// SimilarRequest 相似搜索请求
type SimilarRequest struct {
	News  models.NewsItem `json:"news"`
	Limit int             `json:"limit"`
}

// searchHandler 搜索处理器
func (s *Server) searchHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取查询参数
	text := r.URL.Query().Get("text")
	if text == "" {
		http.Error(w, "Missing text parameter", http.StatusBadRequest)
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 10 // 默认限制
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// 检查向量服务是否可用
	if s.vectorService == nil {
		http.Error(w, "Vector search service not available", http.StatusServiceUnavailable)
		return
	}

	// 执行搜索
	results, err := s.vectorService.SearchByText(text, limit)
	if err != nil {
		log.Printf("搜索失败: %v", err)
		http.Error(w, "Search failed", http.StatusInternalServerError)
		return
	}

	// 返回结果
	response := map[string]interface{}{
		"query":   text,
		"limit":   limit,
		"results": results,
		"count":   len(results),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// statsHandler 统计信息处理器
func (s *Server) statsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 添加基本信息
	response := map[string]interface{}{
		"timestamp": time.Now().Unix(),
	}

	// 获取向量统计信息（如果可用）
	if s.vectorService != nil {
		stats, err := s.vectorService.GetCollectionStats()
		if err != nil {
			log.Printf("获取向量统计信息失败: %v", err)
			response["vector_stats"] = "error: " + err.Error()
		} else {
			response["vector_dimension"] = s.vectorService.GetVectorDimension()
			response["collection_stats"] = stats
		}
	} else {
		response["vector_stats"] = "disabled"
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// similarHandler 相似搜索处理器
func (s *Server) similarHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求
	var request SimilarRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		log.Printf("解析请求失败: %v", err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// 检查向量服务是否可用
	if s.vectorService == nil {
		http.Error(w, "Vector search service not available", http.StatusServiceUnavailable)
		return
	}

	// 设置默认限制
	if request.Limit <= 0 || request.Limit > 100 {
		request.Limit = 10
	}

	// 执行相似搜索
	results, err := s.vectorService.SearchSimilarNews(request.News, request.Limit)
	if err != nil {
		log.Printf("相似搜索失败: %v", err)
		http.Error(w, "Similar search failed", http.StatusInternalServerError)
		return
	}

	// 返回结果
	response := map[string]interface{}{
		"news":    request.News,
		"limit":   request.Limit,
		"results": results,
		"count":   len(results),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// qualitySessionsHandler 获取爬取会话列表
func (s *Server) qualitySessionsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取查询参数
	limitStr := r.URL.Query().Get("limit")
	limit := 20 // 默认限制
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// 查询会话列表
	sessions, err := svcCtx.CrawlSessionModel.GetCrawlSessions(limit)
	if err != nil {
		log.Printf("查询爬取会话失败: %v", err)
		http.Error(w, "Failed to get sessions", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(sessions)
}

// qualityNewsHandler 获取指定会话的新闻列表
func (s *Server) qualityNewsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取查询参数
	sessionIDStr := r.URL.Query().Get("session_id")
	if sessionIDStr == "" {
		http.Error(w, "Missing session_id parameter", http.StatusBadRequest)
		return
	}

	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid session_id", http.StatusBadRequest)
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 50 // 默认限制
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 200 {
			limit = l
		}
	}

	// 查询新闻列表
	news, err := svcCtx.NewsRecordModel.GetNewsRecordsBySession(sessionID, limit)
	if err != nil {
		log.Printf("查询新闻记录失败: %v", err)
		http.Error(w, "Failed to get news", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(news)
}

// qualityStatsHandler 获取质量统计信息
func (s *Server) qualityStatsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取统计信息
	stats, err := svcCtx.NewsRecordModel.GetQualityStats()
	if err != nil {
		log.Printf("获取质量统计失败: %v", err)
		http.Error(w, "Failed to get stats", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// qualityDetailHandler 获取新闻详情
func (s *Server) qualityDetailHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取查询参数
	idStr := r.URL.Query().Get("id")
	if idStr == "" {
		http.Error(w, "Missing id parameter", http.StatusBadRequest)
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid id", http.StatusBadRequest)
		return
	}

	// 查询新闻详情
	news, err := svcCtx.NewsRecordModel.GetNewsRecordByID(id)
	if err != nil {
		log.Printf("查询新闻详情失败: %v", err)
		http.Error(w, "Failed to get news detail", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(news)
}

// articleDetailHandler 文章详情页面处理器
func (s *Server) articleDetailHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 从URL路径中提取文章ID
	path := r.URL.Path
	if !strings.HasPrefix(path, "/article/") {
		http.Error(w, "Invalid article URL", http.StatusBadRequest)
		return
	}

	idStr := strings.TrimPrefix(path, "/article/")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid article ID", http.StatusBadRequest)
		return
	}

	// 查询文章详情
	news, err := svcCtx.NewsRecordModel.GetNewsRecordByID(id)
	if err != nil {
		log.Printf("查询文章详情失败: %v", err)
		http.Error(w, "Article not found", http.StatusNotFound)
		return
	}

	// 生成文章详情页面HTML
	html := s.generateArticleDetailHTML(news)

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// generateArticleDetailHTML 生成文章详情页面HTML
func (s *Server) generateArticleDetailHTML(news *model.NewsRecord) string {
	// 计算质量等级
	qualityClass := "score-low"
	qualityText := "低质量"
	if news.QualityScore >= 70 {
		qualityClass = "score-high"
		qualityText = "高质量"
	} else if news.QualityScore >= 40 {
		qualityClass = "score-medium"
		qualityText = "中等质量"
	}

	// 处理内容，确保换行正确显示
	content := strings.ReplaceAll(news.Content, "\n", "<br>")
	if content == "" {
		content = "<p style='color: #999; font-style: italic;'>暂无完整内容</p>"
	}

	// 处理摘要
	summary := news.Summary
	if summary == "" {
		summary = "暂无摘要"
	}

	// 处理作者信息
	author := news.Author
	if author == "" {
		author = "未知"
	}

	// 构建HTML
	html := fmt.Sprintf(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>%s - 新闻详情</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.3;
        }
        .meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .meta-item {
            display: flex;
            align-items: center;
        }
        .meta-label {
            font-weight: bold;
            margin-right: 5px;
        }
        .quality-score {
            padding: 4px 12px;
            border-radius: 15px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .score-high { background-color: #4CAF50; }
        .score-medium { background-color: #FF9800; }
        .score-low { background-color: #f44336; }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .content {
            font-size: 16px;
            color: #333;
            text-align: justify;
            word-break: break-word;
        }
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #2196F3;
            font-size: 15px;
            color: #555;
        }
        .back-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .back-btn:hover {
            background: #1976D2;
        }
        .url-link {
            color: #2196F3;
            text-decoration: none;
            word-break: break-all;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        .stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #2196F3;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="back-btn" onclick="window.close()">← 返回</button>

        <div class="header">
            <h1 class="title">%s</h1>
            <div class="meta">
                <div class="meta-item">
                    <span class="meta-label">来源:</span>
                    <span>%s</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">发布时间:</span>
                    <span>%s</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">作者:</span>
                    <span>%s</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">质量评分:</span>
                    <span class="quality-score %s">%.1f (%s)</span>
                </div>
            </div>
            <div class="meta">
                <div class="meta-item">
                    <span class="meta-label">原文链接:</span>
                    <a href="%s" target="_blank" class="url-link">%s</a>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">内容摘要</h2>
            <div class="summary">%s</div>
        </div>

        <div class="section">
            <h2 class="section-title">完整内容</h2>
            <div class="content">%s</div>
        </div>

        <div class="stats">
            <h3 style="margin-top: 0; margin-bottom: 15px; color: #333;">文章统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">%d</div>
                    <div class="stat-label">字数统计</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">%d</div>
                    <div class="stat-label">内容长度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">%s</div>
                    <div class="stat-label">内容状态</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">%s</div>
                    <div class="stat-label">抓取时间</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`,
		news.Title, // title tag
		news.Title, // h1 title
		news.Source,
		news.PublishTime,
		author,
		qualityClass, news.QualityScore, qualityText,
		news.URL, news.URL,
		summary,
		content,
		news.WordCount,
		news.ContentLength,
		func() string {
			if news.HasContent {
				return "完整"
			}
			return "不完整"
		}(),
		news.CreatedAt.Format("01-02 15:04"),
	)

	return html
}

// hotlistSessionsHandler 获取热搜会话列表
func (s *Server) hotlistSessionsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取查询参数
	limitStr := r.URL.Query().Get("limit")
	limit := 20 // 默认限制
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// 查询热搜会话列表
	sessions, err := svcCtx.HotlistSessionModel.GetHotlistSessions(limit)
	if err != nil {
		log.Printf("查询热搜会话失败: %v", err)
		http.Error(w, "Failed to get hotlist sessions", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(sessions)
}

// hotlistRecordsHandler 获取指定会话的热搜记录
func (s *Server) hotlistRecordsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取查询参数
	sessionIDStr := r.URL.Query().Get("session_id")
	if sessionIDStr == "" {
		http.Error(w, "Missing session_id parameter", http.StatusBadRequest)
		return
	}

	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid session_id", http.StatusBadRequest)
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 100 // 默认限制
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 500 {
			limit = l
		}
	}

	// 查询热搜记录列表
	records, err := svcCtx.HotlistRecordModel.GetHotlistRecordsBySession(sessionID, limit)
	if err != nil {
		log.Printf("查询热搜记录失败: %v", err)
		http.Error(w, "Failed to get hotlist records", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(records)
}

// hotlistStatsHandler 获取热搜统计信息
func (s *Server) hotlistStatsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取热搜统计信息
	stats, err := svcCtx.HotlistRecordModel.GetHotlistStats()
	if err != nil {
		log.Printf("获取热搜统计失败: %v", err)
		http.Error(w, "Failed to get hotlist stats", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// topicAggregationRecordsHandler 获取话题合并记录
func (s *Server) topicAggregationRecordsHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取limit参数
	limitStr := r.URL.Query().Get("limit")
	limit := 20 // 默认值
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	// 获取话题合并记录
	records, err := svcCtx.TopicAggregationModel.GetTopicAggregationRecords(limit)
	if err != nil {
		log.Printf("获取话题合并记录失败: %v", err)
		http.Error(w, "Failed to get topic aggregation records", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(records)
}

// vectorSearchRecordsHandler 获取向量搜索记录
func (s *Server) vectorSearchRecordsHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取limit参数
	limitStr := r.URL.Query().Get("limit")
	limit := 20 // 默认值
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	// 获取向量搜索记录
	records, err := svcCtx.VectorSearchModel.GetVectorSearchRecords(limit)
	if err != nil {
		log.Printf("获取向量搜索记录失败: %v", err)
		http.Error(w, "Failed to get vector search records", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(records)
}

// generatedArticleRecordsHandler 获取生成文章记录
func (s *Server) generatedArticleRecordsHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取limit参数
	limitStr := r.URL.Query().Get("limit")
	limit := 20 // 默认值
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	// 获取生成文章记录
	records, err := svc.SvcCtx.GeneratedArticleModel.GetGeneratedArticleRecords(limit)
	if err != nil {
		log.Printf("获取生成文章记录失败: %v", err)
		http.Error(w, "Failed to get generated article records", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(records)
}

// topicAggregationBySessionHandler 根据会话ID获取话题合并记录
func (s *Server) topicAggregationBySessionHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取session_id参数
	sessionIdStr := r.URL.Query().Get("session_id")
	if sessionIdStr == "" {
		http.Error(w, "Missing session_id parameter", http.StatusBadRequest)
		return
	}

	sessionId, err := strconv.ParseInt(sessionIdStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid session_id parameter", http.StatusBadRequest)
		return
	}

	// 获取该会话的话题合并记录
	records, err := svcCtx.TopicAggregationModel.GetTopicAggregationRecordsBySession(sessionId)
	if err != nil {
		log.Printf("获取会话话题合并记录失败: %v", err)
		http.Error(w, "Failed to get topic aggregation records", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(records)
}

// vectorSearchByTopicHandler 根据话题ID获取向量搜索记录
func (s *Server) vectorSearchByTopicHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取topic_id参数
	topicId := r.URL.Query().Get("topic_id")
	if topicId == "" {
		http.Error(w, "Missing topic_id parameter", http.StatusBadRequest)
		return
	}

	// 获取该话题的向量搜索记录
	records, err := svcCtx.VectorSearchModel.GetVectorSearchRecordsByTopic(topicId)
	if err != nil {
		log.Printf("获取话题向量搜索记录失败: %v", err)
		http.Error(w, "Failed to get vector search records", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(records)
}

// generatedArticlesByTopicHandler 根据话题ID获取生成文章记录
func (s *Server) generatedArticlesByTopicHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取topic_id参数
	topicId := r.URL.Query().Get("topic_id")
	if topicId == "" {
		http.Error(w, "Missing topic_id parameter", http.StatusBadRequest)
		return
	}

	// 获取该话题的生成文章记录
	records, err := svcCtx.GeneratedArticleModel.GetGeneratedArticleRecordsByTopic(topicId)
	if err != nil {
		log.Printf("获取话题生成文章记录失败: %v", err)
		http.Error(w, "Failed to get generated article records", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(records)
}

// searchResultsHandler 获取搜索结果详情
func (s *Server) searchResultsHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取search_id参数
	searchIdStr := r.URL.Query().Get("search_id")
	if searchIdStr == "" {
		http.Error(w, "Missing search_id parameter", http.StatusBadRequest)
		return
	}

	searchId, err := strconv.ParseInt(searchIdStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid search_id parameter", http.StatusBadRequest)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取搜索记录
	searchRecord, err := svcCtx.VectorSearchModel.GetById(searchId)
	if err != nil {
		log.Printf("获取搜索记录失败: %v", err)
		http.Error(w, "Failed to get search record", http.StatusInternalServerError)
		return
	}

	if searchRecord == nil {
		http.Error(w, "Search record not found", http.StatusNotFound)
		return
	}

	// 解析搜索结果URL
	var resultUrls []string
	if searchRecord.ResultURLs != "" {
		if err := json.Unmarshal([]byte(searchRecord.ResultURLs), &resultUrls); err != nil {
			log.Printf("解析搜索结果URL失败: %v", err)
			resultUrls = []string{}
		}
	}

	// 根据URL获取新闻详情
	var newsResults []map[string]interface{}
	for _, url := range resultUrls {
		// 这里可以根据URL查询新闻详情，暂时返回URL
		newsResults = append(newsResults, map[string]interface{}{
			"url":   url,
			"title": "新闻标题", // 实际应该从数据库查询
		})
	}

	response := map[string]interface{}{
		"search_record": searchRecord,
		"results":       newsResults,
		"result_count":  len(newsResults),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// generatedArticleDetailHandler 获取生成文章详情
func (s *Server) generatedArticleDetailHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取article_id参数
	articleIdStr := r.URL.Query().Get("article_id")
	if articleIdStr == "" {
		http.Error(w, "Missing article_id parameter", http.StatusBadRequest)
		return
	}

	articleId, err := strconv.ParseInt(articleIdStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid article_id parameter", http.StatusBadRequest)
		return
	}

	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		http.Error(w, "Database not available", http.StatusServiceUnavailable)
		return
	}

	// 获取文章详情
	article, err := svcCtx.GeneratedArticleModel.GetById(articleId)
	if err != nil {
		log.Printf("获取文章详情失败: %v", err)
		http.Error(w, "Failed to get article detail", http.StatusInternalServerError)
		return
	}

	if article == nil {
		http.Error(w, "Article not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(article)
}
