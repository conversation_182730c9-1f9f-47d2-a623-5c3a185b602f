<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻爬虫质量检测系统</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        .header { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            margin-bottom: 20px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 20px; 
        }
        .stat-card { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .stat-value { 
            font-size: 2em; 
            font-weight: bold; 
            color: #2196F3; 
        }
        .stat-label { 
            color: #666; 
            margin-top: 5px; 
        }
        .sessions-section { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .session-item { 
            border-bottom: 1px solid #eee; 
            padding: 15px 0; 
            cursor: pointer; 
        }
        .session-item:hover { 
            background-color: #f9f9f9; 
        }
        .session-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .session-time { 
            font-weight: bold; 
        }
        .session-stats { 
            color: #666; 
            margin-top: 5px; 
        }
        .status-completed { color: #4CAF50; }
        .status-failed { color: #f44336; }
        .status-partial { color: #FF9800; }
        .news-list { 
            margin-top: 10px; 
            display: none; 
        }
        .news-item { 
            background: #f9f9f9; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 4px; 
        }
        .quality-score { 
            float: right; 
            padding: 2px 8px; 
            border-radius: 12px; 
            color: white; 
            font-size: 0.8em; 
        }
        .score-high { background-color: #4CAF50; }
        .score-medium { background-color: #FF9800; }
        .score-low { background-color: #f44336; }
        .loading { 
            text-align: center; 
            padding: 20px; 
            color: #666; 
        }
        .refresh-btn { 
            background: #2196F3; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
        }
        .refresh-btn:hover { 
            background: #1976D2; 
        }
        .detail-btn {
            margin-left: 10px; 
            padding: 2px 8px; 
            background: #2196F3; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer; 
            font-size: 0.8em;
        }
        .detail-btn:hover {
            background: #1976D2;
        }
        .tab-btn {
            background: #f5f5f5;
            color: #666;
            border: none;
            padding: 10px 20px;
            margin-right: 10px;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            font-size: 14px;
        }
        .tab-btn.active {
            background: #2196F3;
            color: white;
        }
        .tab-btn:hover {
            background: #e0e0e0;
        }
        .tab-btn.active:hover {
            background: #1976D2;
        }
        .tab-content {
            border-top: 2px solid #2196F3;
            padding-top: 20px;
        }

        /* 工作流程样式 */
        .workflow-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .workflow-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30px 20px;
            position: relative;
        }
        .workflow-step {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .step-icon.step-1 { background: #e74c3c; }
        .step-icon.step-2 { background: #f39c12; }
        .step-icon.step-3 { background: #3498db; }
        .step-icon.step-4 { background: #27ae60; }
        .step-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .step-desc {
            font-size: 12px;
            color: #666;
        }
        .workflow-arrow {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(to right, #e74c3c, #f39c12, #3498db, #27ae60);
            z-index: 1;
            margin: 0 80px;
        }
        .workflow-content {
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .step-details {
            display: none;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .step-details.active {
            display: block;
        }
        .step-data {
            background: white;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }
        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .data-item:last-child {
            border-bottom: none;
        }
        .data-label {
            font-weight: bold;
            color: #555;
        }
        .data-value {
            color: #333;
        }
        .topic-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        .topic-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: #2196F3;
        }
        .topic-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .topic-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }
        .search-result {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
        }
        .search-result-title {
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 5px;
        }
        .search-result-meta {
            font-size: 12px;
            color: #666;
        }
        .article-preview {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .article-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .article-content {
            color: #555;
            line-height: 1.6;
            max-height: 200px;
            overflow-y: auto;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hotlist-item {
            background: #f9f9f9;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .hotlist-title {
            font-weight: bold;
            flex: 1;
        }
        .hotlist-meta {
            color: #666;
            font-size: 0.9em;
            margin-left: 10px;
        }
        .platform-tag {
            padding: 2px 8px;
            border-radius: 12px;
            color: white;
            font-size: 0.8em;
            margin-right: 10px;
        }
        .platform-weibo { background-color: #e6162d; }
        .platform-zhihu { background-color: #0084ff; }
        .platform-baidu { background-color: #2932e1; }
        .platform-default { background-color: #666; }

        /* 树形工作流程样式 */
        .workflow-tree {
            margin: 20px 0;
        }

        .tree-node {
            margin-bottom: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }

        .tree-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            cursor: pointer;
            background: white;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .tree-item:hover {
            background: #f5f5f5;
        }

        .tree-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .tree-label {
            flex: 1;
            font-weight: 500;
            color: #333;
        }

        .tree-count {
            margin-right: 10px;
            background: #2196F3;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            min-width: 20px;
            text-align: center;
        }

        .tree-toggle {
            font-size: 12px;
            color: #666;
            transition: transform 0.2s;
        }

        .tree-toggle.expanded {
            transform: rotate(180deg);
        }

        .tree-content {
            display: none;
            padding: 16px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .tree-content.expanded {
            display: block;
        }


    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>新闻爬虫质量检测系统</h1>
            <p>实时监控新闻抓取质量和内容完整性</p>
            <button class="refresh-btn" onclick="loadData()">刷新数据</button>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <div class="loading">正在加载统计数据...</div>
        </div>
        
        <!-- 树形工作流程展示 -->
        <div class="workflow-container">
            <div class="workflow-header">
                <h2>🔥 热搜内容生成工作流</h2>
            </div>

            <!-- 树形工作流程 -->
            <div class="workflow-tree">
                <div class="tree-node">
                    <div class="tree-item" onclick="toggleTreeNode('hotlist')">
                        <span class="tree-icon">📊</span>
                        <span class="tree-label">热搜抓取</span>
                        <span class="tree-toggle" id="hotlist-toggle">▼</span>
                        <span class="tree-count" id="hotlist-count">-</span>
                    </div>
                    <div class="tree-content" id="hotlist-content">
                        <div id="hotlistData" class="loading">正在加载...</div>
                    </div>
                </div>

                <div class="tree-node">
                    <div class="tree-item" onclick="toggleTreeNode('topics')">
                        <span class="tree-icon">🤖</span>
                        <span class="tree-label">话题合并</span>
                        <span class="tree-toggle" id="topics-toggle">▼</span>
                        <span class="tree-count" id="topics-count">-</span>
                    </div>
                    <div class="tree-content" id="topics-content">
                        <div id="topicAggregationData" class="loading">正在加载...</div>
                    </div>
                </div>

                <div class="tree-node">
                    <div class="tree-item" onclick="toggleTreeNode('search')">
                        <span class="tree-icon">🔍</span>
                        <span class="tree-label">向量搜索</span>
                        <span class="tree-toggle" id="search-toggle">▼</span>
                        <span class="tree-count" id="search-count">-</span>
                    </div>
                    <div class="tree-content" id="search-content">
                        <div id="vectorSearchData" class="loading">正在加载...</div>
                    </div>
                </div>

                <div class="tree-node">
                    <div class="tree-item" onclick="toggleTreeNode('articles')">
                        <span class="tree-icon">📝</span>
                        <span class="tree-label">文章生成</span>
                        <span class="tree-toggle" id="articles-toggle">▼</span>
                        <span class="tree-count" id="articles-count">-</span>
                    </div>
                    <div class="tree-content" id="articles-content">
                        <div id="articleGenerationData" class="loading">正在加载...</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="sessions-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div>
                    <button class="tab-btn active" onclick="switchTab('news')">新闻爬取</button>
                    <button class="tab-btn" onclick="switchTab('workflow')">工作流历史</button>
                </div>
            </div>

            <div id="newsTab" class="tab-content">
                <h2>新闻爬取会话历史</h2>
                <div id="sessionsList">
                    <div class="loading">正在加载会话数据...</div>
                </div>
            </div>

            <div id="workflowTab" class="tab-content" style="display: none;">
                <h2>热搜工作流历史记录</h2>
                <div id="workflowSessionsList">
                    <div class="loading">正在加载工作流历史数据...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
        });
        
        async function loadData() {
            try {
                await loadStats();
                await loadSessions();
                await loadWorkflowData();
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        // 加载工作流数据（树形展示）
        async function loadWorkflowData() {
            try {
                // 更新计数
                await updateTreeCounts();

                // 加载工作流历史（用于第二个标签页）
                await loadWorkflowSessions();

            } catch (error) {
                console.error('加载工作流数据失败:', error);
            }
        }

        // 更新树形节点计数
        async function updateTreeCounts() {
            try {
                const [hotlistResponse, topicsResponse, searchResponse, articlesResponse] = await Promise.all([
                    fetch('/hotlist/sessions?limit=1'),
                    fetch('/llm/topic-aggregation?limit=1'),
                    fetch('/llm/vector-search?limit=10'),
                    fetch('/llm/generated-articles?limit=10')
                ]);

                const [hotlistSessions, topicRecords, searchRecords, articles] = await Promise.all([
                    hotlistResponse.json(),
                    topicsResponse.json(),
                    searchResponse.json(),
                    articlesResponse.json()
                ]);

                // 更新计数显示
                document.getElementById('hotlist-count').textContent = hotlistSessions.length > 0 ? hotlistSessions[0].total_items : '0';
                document.getElementById('topics-count').textContent = topicRecords.length > 0 ? JSON.parse(topicRecords[0].output_topics || '[]').length : '0';
                document.getElementById('search-count').textContent = searchRecords.length;
                document.getElementById('articles-count').textContent = articles.length;

            } catch (error) {
                console.error('更新计数失败:', error);
            }
        }

        // 切换树形节点展开/收起
        function toggleTreeNode(nodeId) {
            const content = document.getElementById(nodeId + '-content');
            const toggle = document.getElementById(nodeId + '-toggle');

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                toggle.classList.remove('expanded');
                toggle.textContent = '▼';
            } else {
                content.classList.add('expanded');
                toggle.classList.add('expanded');
                toggle.textContent = '▲';

                // 加载对应的数据
                loadTreeNodeData(nodeId);
            }
        }

        // 加载树形节点数据
        async function loadTreeNodeData(nodeId) {
            switch(nodeId) {
                case 'hotlist':
                    await loadLatestHotlistData();
                    break;
                case 'topics':
                    await loadLatestTopicAggregation();
                    break;
                case 'search':
                    await loadLatestVectorSearch();
                    break;
                case 'articles':
                    await loadLatestArticleGeneration();
                    break;
            }
        }
        
        async function loadStats() {
            try {
                const response = await fetch('/quality/stats');
                const stats = await response.json();
                displayStats(stats);
            } catch (error) {
                console.error('加载统计数据失败:', error);
                document.getElementById('statsGrid').innerHTML = '<div class="loading">统计数据加载失败</div>';
            }
        }
        
        async function loadSessions() {
            try {
                const response = await fetch('/quality/sessions?limit=20');
                const sessions = await response.json();
                displaySessions(sessions);
            } catch (error) {
                console.error('加载会话数据失败:', error);
                document.getElementById('sessionsList').innerHTML = '<div class="loading">会话数据加载失败</div>';
            }
        }
        
        function displayStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.total_news || 0}</div>
                    <div class="stat-label">总新闻数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.success_news || 0}</div>
                    <div class="stat-label">有效新闻数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${(stats.success_rate || 0).toFixed(1)}%</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${(stats.avg_quality || 0).toFixed(1)}</div>
                    <div class="stat-label">平均质量分</div>
                </div>
            `;
        }
        
        function displaySessions(sessions) {
            const sessionsList = document.getElementById('sessionsList');
            if (!sessions || sessions.length === 0) {
                sessionsList.innerHTML = '<div class="loading">暂无会话数据</div>';
                return;
            }
            
            sessionsList.innerHTML = sessions.map(session => `
                <div class="session-item" onclick="toggleNews(${session.id})">
                    <div class="session-header">
                        <div>
                            <div class="session-time">${formatTime(session.start_time)}</div>
                            <div class="session-stats">
                                总计: ${session.total_news} 条 | 成功: ${session.success_news} 条 | 
                                <span class="status-${session.status}">${getStatusText(session.status)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="news-list" id="news-${session.id}"></div>
                </div>
            `).join('');
        }
        
        async function toggleNews(sessionId) {
            const newsDiv = document.getElementById(`news-${sessionId}`);
            if (newsDiv.style.display === 'block') {
                newsDiv.style.display = 'none';
                return;
            }
            
            if (newsDiv.innerHTML === '') {
                newsDiv.innerHTML = '<div class="loading">正在加载新闻列表...</div>';
                try {
                    const response = await fetch(`/quality/news?session_id=${sessionId}&limit=50`);
                    const news = await response.json();
                    displayNews(newsDiv, news);
                } catch (error) {
                    newsDiv.innerHTML = '<div class="loading">新闻数据加载失败</div>';
                }
            }
            newsDiv.style.display = 'block';
        }
        
        function displayNews(container, newsList) {
            if (!newsList || newsList.length === 0) {
                container.innerHTML = '<div class="loading">该会话暂无新闻数据</div>';
                return;
            }
            
            container.innerHTML = newsList.map(news => `
                <div class="news-item">
                    <div>
                        <strong>${news.title}</strong>
                        <span class="quality-score ${getScoreClass(news.quality_score)}">${news.quality_score.toFixed(1)}</span>
                    </div>
                    <div style="color: #666; font-size: 0.9em; margin-top: 5px;">
                        来源: ${news.source} | 字数: ${news.word_count} | 
                        内容: ${news.has_content ? '完整' : '不完整'}
                        <button class="detail-btn" onclick="viewArticle(${news.id})">查看详情</button>
                    </div>
                </div>
            `).join('');
        }
        
        function formatTime(timeStr) {
            return new Date(timeStr).toLocaleString('zh-CN');
        }
        
        function getStatusText(status) {
            const statusMap = {
                'completed': '完成',
                'failed': '失败', 
                'partial': '部分成功',
                'running': '运行中'
            };
            return statusMap[status] || status;
        }
        
        function getScoreClass(score) {
            if (score >= 70) return 'score-high';
            if (score >= 40) return 'score-medium';
            return 'score-low';
        }
        
        function viewArticle(articleId) {
            window.open('/article/' + articleId, '_blank');
        }

        // 标签页切换
        function switchTab(tabName) {
            // 更新按钮状态
            document.querySelectorAll('.sessions-section .tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 切换内容
            document.getElementById('newsTab').style.display = tabName === 'news' ? 'block' : 'none';
            document.getElementById('workflowTab').style.display = tabName === 'workflow' ? 'block' : 'none';
        }

        // 显示步骤详情
        function showStepDetails(stepNumber) {
            // 隐藏所有步骤详情
            document.querySelectorAll('.step-details').forEach(detail => {
                detail.classList.remove('active');
            });

            // 显示选中的步骤详情
            document.getElementById(`step${stepNumber}Details`).classList.add('active');
        }

        // 加载最新热搜数据
        async function loadLatestHotlistData() {
            try {
                const response = await fetch('/hotlist/sessions?limit=1');
                const sessions = await response.json();

                if (sessions && sessions.length > 0) {
                    const latestSession = sessions[0];
                    const recordsResponse = await fetch(`/hotlist/records?session_id=${latestSession.id}&limit=50`);
                    const records = await recordsResponse.json();

                    displayHotlistWorkflowData(latestSession, records);
                } else {
                    document.getElementById('hotlistData').innerHTML = '<div class="loading">暂无热搜数据</div>';
                }
            } catch (error) {
                console.error('加载热搜数据失败:', error);
                document.getElementById('hotlistData').innerHTML = '<div class="loading">热搜数据加载失败</div>';
            }
        }

        // 显示热搜工作流数据
        function displayHotlistWorkflowData(session, records) {
            const container = document.getElementById('hotlistData');

            if (!records || records.length === 0) {
                container.innerHTML = '<div class="loading">暂无热搜记录</div>';
                return;
            }

            // 按平台分组
            const groupedRecords = {};
            records.forEach(record => {
                if (!groupedRecords[record.platform]) {
                    groupedRecords[record.platform] = [];
                }
                groupedRecords[record.platform].push(record);
            });

            let html = `
                <div class="step-data">
                    <div class="data-item">
                        <span class="data-label">抓取时间:</span>
                        <span class="data-value">${formatTime(session.start_time)}</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">总计条数:</span>
                        <span class="data-value">${session.total_items} 条</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">成功条数:</span>
                        <span class="data-value">${session.success_items} 条</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">状态:</span>
                        <span class="data-value status-${session.status}">${getStatusText(session.status)}</span>
                    </div>
                </div>
            `;

            Object.keys(groupedRecords).forEach(platform => {
                html += `<h4 style="margin: 15px 0 10px 0; color: #333;">${platform} (${groupedRecords[platform].length}条)</h4>`;
                html += '<div style="max-height: 300px; overflow-y: auto;">';
                html += groupedRecords[platform].slice(0, 10).map(record => `
                    <div class="hotlist-item">
                        <div class="hotlist-title">
                            <span class="platform-tag platform-${getPlatformClass(record.platform)}">#${record.rank}</span>
                            ${record.title}
                        </div>
                        <div class="hotlist-meta">
                            热度: ${formatHotValue(record.hot_value)}
                        </div>
                    </div>
                `).join('');
                if (groupedRecords[platform].length > 10) {
                    html += `<div style="text-align: center; padding: 10px; color: #666;">还有 ${groupedRecords[platform].length - 10} 条记录...</div>`;
                }
                html += '</div>';
            });

            container.innerHTML = html;
        }

        // 加载最新话题聚合数据
        async function loadLatestTopicAggregation() {
            try {
                const response = await fetch('/llm/topic-aggregation?limit=1');
                const records = await response.json();

                if (records && records.length > 0) {
                    displayTopicAggregationWorkflowData(records[0]);
                } else {
                    document.getElementById('topicAggregationData').innerHTML = '<div class="loading">暂无话题聚合数据</div>';
                }
            } catch (error) {
                console.error('加载话题聚合数据失败:', error);
                document.getElementById('topicAggregationData').innerHTML = '<div class="loading">话题聚合数据加载失败</div>';
            }
        }

        // 显示话题聚合工作流数据
        function displayTopicAggregationWorkflowData(record) {
            const container = document.getElementById('topicAggregationData');

            let topics = [];
            try {
                topics = JSON.parse(record.output_topics || '[]');
            } catch (e) {
                console.error('解析话题数据失败:', e);
            }

            let html = `
                <div class="step-data">
                    <div class="data-item">
                        <span class="data-label">处理时间:</span>
                        <span class="data-value">${formatTime(record.created_at)}</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">输入项目:</span>
                        <span class="data-value">${record.input_items_count} 个热搜项</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">输出话题:</span>
                        <span class="data-value">${topics.length} 个话题</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">LLM模型:</span>
                        <span class="data-value">${record.llm_model}</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">处理耗时:</span>
                        <span class="data-value">${record.process_time.toFixed(2)} 秒</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">状态:</span>
                        <span class="data-value status-${record.status}">${getStatusText(record.status)}</span>
                    </div>
                </div>
            `;

            if (topics.length > 0) {
                html += '<h4 style="margin: 15px 0 10px 0; color: #333;">合并后的话题</h4>';
                html += '<div style="max-height: 400px; overflow-y: auto;">';
                html += topics.map(topic => `
                    <div class="topic-card">
                        <div class="topic-title">${topic.title || topic.name || '未知话题'}</div>
                        <div class="topic-desc">${topic.description || topic.desc || '暂无描述'}</div>
                    </div>
                `).join('');
                html += '</div>';
            }

            if (record.error_msg) {
                html += `<div style="color: #f44336; margin-top: 10px; padding: 10px; background: #ffebee; border-radius: 4px;">错误: ${record.error_msg}</div>`;
            }

            container.innerHTML = html;
        }

        // 加载最新向量搜索数据
        async function loadLatestVectorSearch() {
            try {
                const response = await fetch('/llm/vector-search?limit=5');
                const records = await response.json();

                if (records && records.length > 0) {
                    displayVectorSearchWorkflowData(records);
                } else {
                    document.getElementById('vectorSearchData').innerHTML = '<div class="loading">暂无向量搜索数据</div>';
                }
            } catch (error) {
                console.error('加载向量搜索数据失败:', error);
                document.getElementById('vectorSearchData').innerHTML = '<div class="loading">向量搜索数据加载失败</div>';
            }
        }

        // 显示向量搜索工作流数据
        function displayVectorSearchWorkflowData(records) {
            const container = document.getElementById('vectorSearchData');

            let html = `
                <div class="step-data">
                    <div class="data-item">
                        <span class="data-label">最近搜索:</span>
                        <span class="data-value">${records.length} 次搜索</span>
                    </div>
                </div>
            `;

            html += '<h4 style="margin: 15px 0 10px 0; color: #333;">最近搜索记录</h4>';
            html += '<div style="max-height: 400px; overflow-y: auto;">';
            html += records.map(record => {
                let resultUrls = [];
                try {
                    resultUrls = JSON.parse(record.result_urls || '[]');
                } catch (e) {
                    console.error('解析搜索结果失败:', e);
                }

                return `
                    <div class="search-result">
                        <div class="search-result-title">话题: ${record.topic_title}</div>
                        <div style="margin: 8px 0; color: #555;">搜索查询: ${record.search_query}</div>
                        <div class="search-result-meta">
                            搜索时间: ${formatTime(record.created_at)} |
                            结果数量: ${record.result_count} 条 |
                            耗时: ${record.search_time.toFixed(2)} 秒 |
                            <span class="status-${record.status}">${getStatusText(record.status)}</span>
                        </div>
                        ${record.error_msg ? `<div style="color: #f44336; font-size: 12px; margin-top: 5px;">错误: ${record.error_msg}</div>` : ''}
                        ${resultUrls.length > 0 ? `<div style="margin-top: 8px; font-size: 12px; color: #666;">找到 ${resultUrls.length} 篇相关新闻</div>` : ''}
                    </div>
                `;
            }).join('');
            html += '</div>';

            container.innerHTML = html;
        }

        // 加载最新文章生成数据
        async function loadLatestArticleGeneration() {
            try {
                const response = await fetch('/llm/generated-articles?limit=3');
                const records = await response.json();

                if (records && records.length > 0) {
                    displayArticleData(records);
                } else {
                    document.getElementById('articleGenerationData').innerHTML = '<div class="loading">暂无文章生成数据</div>';
                }
            } catch (error) {
                console.error('加载文章生成数据失败:', error);
                document.getElementById('articleGenerationData').innerHTML = '<div class="loading">文章生成数据加载失败</div>';
            }
        }

        // 显示文章数据（树形节点内容）
        function displayArticleData(records) {
            const container = document.getElementById('articleGenerationData');

            let html = records.map(record => `
                <div style="border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 6px; background: #fafafa;">
                    <div style="font-weight: bold; margin-bottom: 8px; color: #333;">
                        ${record.article_title || '生成失败'}
                    </div>
                    <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
                        话题: ${record.topic_title} | 字数: ${record.word_count} |
                        模型: ${record.llm_model} | 耗时: ${record.generate_time.toFixed(2)}秒 |
                        时间: ${formatTime(record.created_at)} |
                        状态: <span class="status-${record.status}">${getStatusText(record.status)}</span>
                    </div>
                    ${record.article_content ? `
                        <div style="background: white; padding: 12px; border-radius: 4px; border-left: 3px solid #2196F3; line-height: 1.5; font-size: 14px; max-height: 300px; overflow-y: auto;">
                            ${record.article_content}
                        </div>
                    ` : ''}
                    ${record.article_summary ? `
                        <div style="margin-top: 8px; padding: 8px; background: #e8f4f8; border-radius: 4px; font-size: 13px; color: #555; font-style: italic;">
                            摘要: ${record.article_summary}
                        </div>
                    ` : ''}
                    ${record.error_msg ? `
                        <div style="color: #f44336; font-size: 12px; margin-top: 8px;">
                            错误: ${record.error_msg}
                        </div>
                    ` : ''}
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 加载工作流会话历史
        async function loadWorkflowSessions() {
            try {
                const response = await fetch('/hotlist/sessions?limit=10');
                const sessions = await response.json();
                displayWorkflowSessions(sessions);
            } catch (error) {
                console.error('加载工作流会话数据失败:', error);
                document.getElementById('workflowSessionsList').innerHTML = '<div class="loading">工作流会话数据加载失败</div>';
            }
        }

        // 显示工作流会话历史
        function displayWorkflowSessions(sessions) {
            const sessionsList = document.getElementById('workflowSessionsList');
            if (!sessions || sessions.length === 0) {
                sessionsList.innerHTML = '<div class="loading">暂无工作流会话数据</div>';
                return;
            }

            sessionsList.innerHTML = sessions.map(session => `
                <div class="session-item" onclick="toggleWorkflowDetails(${session.id})">
                    <div class="session-header">
                        <div>
                            <div class="session-time">${formatTime(session.start_time)}</div>
                            <div class="session-stats">
                                热搜: ${session.total_items} 条 | 成功: ${session.success_items} 条 |
                                <span class="status-${session.status}">${getStatusText(session.status)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="news-list" id="workflow-${session.id}"></div>
                </div>
            `).join('');
        }

        // 切换工作流详情显示
        async function toggleWorkflowDetails(sessionId) {
            const detailsDiv = document.getElementById(`workflow-${sessionId}`);
            if (detailsDiv.style.display === 'block') {
                detailsDiv.style.display = 'none';
                return;
            }

            if (detailsDiv.innerHTML === '') {
                detailsDiv.innerHTML = '<div class="loading">正在加载工作流详情...</div>';
                try {
                    // 加载该会话的完整工作流数据
                    const [hotlistResponse, topicResponse, searchResponse, articleResponse] = await Promise.all([
                        fetch(`/hotlist/records?session_id=${sessionId}&limit=50`),
                        fetch('/llm/topic-aggregation?limit=10'),
                        fetch('/llm/vector-search?limit=10'),
                        fetch('/llm/generated-articles?limit=10')
                    ]);

                    const [hotlistRecords, topicRecords, searchRecords, articleRecords] = await Promise.all([
                        hotlistResponse.json(),
                        topicResponse.json(),
                        searchResponse.json(),
                        articleResponse.json()
                    ]);

                    displayWorkflowDetails(detailsDiv, sessionId);
                } catch (error) {
                    detailsDiv.innerHTML = '<div class="loading">工作流详情加载失败</div>';
                }
            }
            detailsDiv.style.display = 'block';
        }

        // 显示工作流详情
        async function displayWorkflowDetails(container, sessionId) {
            container.innerHTML = '<div class="loading">正在加载工作流详情...</div>';

            try {
                // 获取该会话的所有数据
                const [hotlistResponse, topicResponse] = await Promise.all([
                    fetch(`/hotlist/records?session_id=${sessionId}&limit=100`),
                    fetch(`/llm/topic-aggregation/session?session_id=${sessionId}`)
                ]);

                const [hotlistRecords, topicRecords] = await Promise.all([
                    hotlistResponse.json(),
                    topicResponse.json()
                ]);

                let html = '<div style="margin-top: 15px;">';

                // 热搜抓取 - 按平台分组显示
                html += '<div><h5 style="margin-bottom: 15px;">🔥 热搜抓取</h5>';
                if (hotlistRecords && hotlistRecords.length > 0) {
                    // 按平台分组
                    const groupedRecords = {};
                    hotlistRecords.forEach(record => {
                        if (!groupedRecords[record.platform]) {
                            groupedRecords[record.platform] = [];
                        }
                        groupedRecords[record.platform].push(record);
                    });

                    Object.keys(groupedRecords).forEach(platform => {
                        const platformRecords = groupedRecords[platform];
                        html += `
                            <div class="platform-section" style="margin-bottom: 15px;">
                                <div class="platform-header" onclick="togglePlatformDetails('${platform}-${sessionId}')"
                                     style="background: #f8f9fa; padding: 10px; border-radius: 6px; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                                    <span><strong>${platform}</strong> (${platformRecords.length} 条)</span>
                                    <span class="toggle-icon" id="icon-${platform}-${sessionId}">▼</span>
                                </div>
                                <div class="platform-details" id="${platform}-${sessionId}" style="display: none; margin-top: 10px; max-height: 300px; overflow-y: auto;">
                                    ${platformRecords.map(record => `
                                        <div class="hotlist-item" style="background: #f9f9f9; padding: 8px; margin: 5px 0; border-radius: 4px; font-size: 12px;">
                                            <div class="hotlist-title">
                                                <span class="platform-tag platform-${getPlatformClass(record.platform)}">#${record.rank}</span>
                                                ${record.title}
                                            </div>
                                            <div class="hotlist-meta" style="color: #666; font-size: 11px; margin-top: 4px;">
                                                热度: ${formatHotValue(record.hot_value)}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += '<div style="color: #666; font-size: 12px;">暂无数据</div>';
                }
                html += '</div>';

                // 话题合并 - 显示当次工作流生成的话题
                html += '<div style="margin-top: 25px;"><h5 style="margin-bottom: 15px;">📝 话题合并</h5>';
                if (topicRecords && topicRecords.length > 0) {
                    // 获取最新的话题合并记录
                    const latestTopicRecord = topicRecords[0];
                    let topics = [];
                    try {
                        topics = JSON.parse(latestTopicRecord.output_topics || '[]');
                    } catch (e) {
                        console.error('解析话题数据失败:', e);
                    }

                    if (topics.length > 0) {
                        html += `<div style="font-size: 12px; color: #666; margin-bottom: 10px;">本次生成 ${topics.length} 个话题</div>`;
                        html += topics.map((topic, index) => `
                            <div class="topic-section" style="margin-bottom: 15px;">
                                <div class="topic-header" onclick="toggleTopicDetails('${topic.id || index}-${sessionId}')"
                                     style="background: #fff3cd; padding: 10px; border-radius: 6px; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong>${topic.title}</strong>
                                        <div style="font-size: 11px; color: #666; margin-top: 4px;">${topic.description || ''}</div>
                                    </div>
                                    <span class="toggle-icon" id="icon-${topic.id || index}-${sessionId}">▼</span>
                                </div>
                                <div class="topic-details" id="${topic.id || index}-${sessionId}" style="display: none; margin-top: 10px;">
                                    <div class="loading">正在加载话题详情...</div>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        html += '<div style="color: #666; font-size: 12px;">暂无话题数据</div>';
                    }
                } else {
                    html += '<div style="color: #666; font-size: 12px;">暂无数据</div>';
                }
                html += '</div>';

                html += '</div>';
                container.innerHTML = html;

            } catch (error) {
                console.error('加载工作流详情失败:', error);
                container.innerHTML = '<div style="color: #f44336;">加载工作流详情失败</div>';
            }
        }

        // 切换平台详情显示
        function togglePlatformDetails(platformId) {
            const detailsDiv = document.getElementById(platformId);
            const iconSpan = document.getElementById('icon-' + platformId);

            if (detailsDiv.style.display === 'none') {
                detailsDiv.style.display = 'block';
                iconSpan.textContent = '▲';
            } else {
                detailsDiv.style.display = 'none';
                iconSpan.textContent = '▼';
            }
        }

        // 切换话题详情显示
        async function toggleTopicDetails(topicId) {
            const detailsDiv = document.getElementById(topicId);
            const iconSpan = document.getElementById('icon-' + topicId);

            if (detailsDiv.style.display === 'none') {
                detailsDiv.style.display = 'block';
                iconSpan.textContent = '▲';

                // 如果还没有加载详情，则加载
                if (detailsDiv.innerHTML.includes('正在加载话题详情')) {
                    await loadTopicDetails(topicId, detailsDiv);
                }
            } else {
                detailsDiv.style.display = 'none';
                iconSpan.textContent = '▼';
            }
        }

        // 加载话题详情
        async function loadTopicDetails(topicId, container) {
            try {
                // 从topicId中提取实际的话题ID（去掉sessionId后缀）
                const actualTopicId = topicId.split('-')[0];

                // 获取该话题的向量搜索记录和生成文章记录
                const [searchResponse, articleResponse] = await Promise.all([
                    fetch(`/llm/vector-search/topic?topic_id=${actualTopicId}`),
                    fetch(`/llm/generated-articles/topic?topic_id=${actualTopicId}`)
                ]);

                const [searchRecords, articleRecords] = await Promise.all([
                    searchResponse.json(),
                    articleResponse.json()
                ]);

                let html = '<div style="padding: 15px; background: #f8f9fa; border-radius: 6px;">';

                // 向量搜索记录
                html += '<div style="margin-bottom: 20px;"><h6 style="margin-bottom: 10px;">🔍 向量搜索记录</h6>';
                if (searchRecords && searchRecords.length > 0) {
                    html += searchRecords.map(record => `
                        <div class="search-record" style="background: #e3f2fd; padding: 10px; margin: 8px 0; border-radius: 4px; cursor: pointer;"
                             onclick="toggleSearchResults(${record.id})">
                            <div style="font-weight: bold; font-size: 13px;">${record.search_query}</div>
                            <div style="font-size: 11px; color: #666; margin-top: 4px;">
                                结果: ${record.result_count} 条 | 耗时: ${record.search_time.toFixed(2)}s |
                                状态: <span class="status-${record.status}">${getStatusText(record.status)}</span>
                            </div>
                            <div class="search-results" id="search-results-${record.id}" style="display: none; margin-top: 10px;">
                                <div class="loading">正在加载搜索结果...</div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    html += '<div style="color: #666; font-size: 12px;">暂无搜索记录</div>';
                }
                html += '</div>';

                // 生成文章记录
                html += '<div><h6 style="margin-bottom: 10px;">📝 生成文章</h6>';
                if (articleRecords && articleRecords.length > 0) {
                    html += articleRecords.map(record => `
                        <div class="article-record" style="background: #e8f5e8; padding: 10px; margin: 8px 0; border-radius: 4px;">
                            <div style="font-weight: bold; font-size: 13px;">${record.article_title}</div>
                            <div style="font-size: 11px; color: #666; margin-top: 4px;">
                                字数: ${record.word_count} | 生成时间: ${record.generate_time.toFixed(2)}s |
                                状态: <span class="status-${record.status}">${getStatusText(record.status)}</span>
                            </div>
                            ${record.article_summary ? `<div style="font-size: 12px; color: #555; margin-top: 6px; font-style: italic;">${record.article_summary}</div>` : ''}
                        </div>
                    `).join('');
                } else {
                    html += '<div style="color: #666; font-size: 12px;">暂无生成文章</div>';
                }
                html += '</div>';

                html += '</div>';
                container.innerHTML = html;

            } catch (error) {
                console.error('加载话题详情失败:', error);
                container.innerHTML = '<div style="color: #f44336;">加载话题详情失败</div>';
            }
        }

        // 切换搜索结果显示
        async function toggleSearchResults(searchId) {
            const resultsDiv = document.getElementById(`search-results-${searchId}`);

            if (resultsDiv.style.display === 'none') {
                resultsDiv.style.display = 'block';

                // 如果还没有加载结果，则加载
                if (resultsDiv.innerHTML.includes('正在加载搜索结果')) {
                    await loadSearchResults(searchId, resultsDiv);
                }
            } else {
                resultsDiv.style.display = 'none';
            }
        }

        // 加载搜索结果
        async function loadSearchResults(searchId, container) {
            try {
                const response = await fetch(`/search/results?search_id=${searchId}`);
                const data = await response.json();

                let html = '<div style="padding: 10px; background: #fff; border-radius: 4px;">';

                if (data.results && data.results.length > 0) {
                    html += `<div style="font-size: 11px; color: #666; margin-bottom: 8px;">找到 ${data.result_count} 条相关新闻</div>`;
                    html += data.results.map(result => `
                        <div style="padding: 6px; margin: 4px 0; background: #f5f5f5; border-radius: 3px; font-size: 11px;">
                            <a href="${result.url}" target="_blank" style="color: #1976d2; text-decoration: none;">
                                ${result.title || result.url}
                            </a>
                        </div>
                    `).join('');
                } else {
                    html += '<div style="color: #666; font-size: 11px;">暂无搜索结果</div>';
                }

                html += '</div>';
                container.innerHTML = html;

            } catch (error) {
                console.error('加载搜索结果失败:', error);
                container.innerHTML = '<div style="color: #f44336;">加载搜索结果失败</div>';
            }
        }

        // 获取平台CSS类名
        function getPlatformClass(platform) {
            const platformMap = {
                '微博': 'weibo',
                '知乎': 'zhihu',
                '百度': 'baidu'
            };
            return platformMap[platform] || 'default';
        }

        // 格式化热度值
        function formatHotValue(value) {
            if (value >= 10000) {
                return (value / 10000).toFixed(1) + '万';
            }
            return Math.round(value).toString();
        }


    </script>
</body>
</html>
