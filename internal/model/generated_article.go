package model

import (
	"time"

	"gorm.io/gorm"
)

// GeneratedArticleRecord 生成文章记录
type GeneratedArticleRecord struct {
	Id             int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`
	TopicId        string    `gorm:"column:topic_id;not null;comment:话题ID" json:"topic_id"`
	TopicTitle     string    `gorm:"column:topic_title;not null;comment:话题标题" json:"topic_title"`
	ArticleTitle   string    `gorm:"column:article_title;not null;comment:文章标题" json:"article_title"`
	ArticleId      string    `gorm:"column:article_id;not null;comment:文章ID" json:"article_id"`
	ArticleContent string    `gorm:"column:article_content;type:text;comment:文章内容" json:"article_content"` // 新增文章内容字段
	ArticleSummary string    `gorm:"column:article_summary;comment:文章摘要" json:"article_summary"`           // 新增文章摘要字段
	WordCount      int       `gorm:"column:word_count;default:0;comment:字数" json:"word_count"`
	RelatedNews    string    `gorm:"column:related_news;comment:相关新闻JSON" json:"related_news"` // JSON格式存储相关新闻URL
	LLMModel       string    `gorm:"column:llm_model;not null;comment:LLM模型" json:"llm_model"`
	GenerateTime   float64   `gorm:"column:generate_time;default:0;comment:生成时间" json:"generate_time"`  // 生成时间（秒）
	Status         string    `gorm:"column:status;not null;default:'success';comment:状态" json:"status"` // success, failed
	ErrorMsg       string    `gorm:"column:error_msg;comment:错误信息" json:"error_msg,omitempty"`
	CreatedAt      time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

// TableName 指定表名
func (GeneratedArticleRecord) TableName() string {
	return "generated_article_records"
}

// GeneratedArticleModel 生成文章记录模型
type GeneratedArticleModel struct {
	db *gorm.DB
}

// NewGeneratedArticleModel 创建生成文章记录模型
func NewGeneratedArticleModel(db *gorm.DB) *GeneratedArticleModel {
	return &GeneratedArticleModel{
		db: db,
	}
}

// Create 创建生成文章记录
func (m *GeneratedArticleModel) Create(record *GeneratedArticleRecord) error {
	return m.db.Create(record).Error
}

// SaveGeneratedArticleRecord 保存生成文章记录
func (m *GeneratedArticleModel) SaveGeneratedArticleRecord(record GeneratedArticleRecord) error {
	record.CreatedAt = time.Now()
	return m.db.Create(&record).Error
}

// GetGeneratedArticleRecords 获取生成文章记录
func (m *GeneratedArticleModel) GetGeneratedArticleRecords(limit int) ([]*GeneratedArticleRecord, error) {
	var records []*GeneratedArticleRecord

	db := m.db.Order("created_at DESC")
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetGeneratedArticleRecordsByTopic 根据话题ID获取生成文章记录
func (m *GeneratedArticleModel) GetGeneratedArticleRecordsByTopic(topicId string) ([]*GeneratedArticleRecord, error) {
	var records []*GeneratedArticleRecord

	if err := m.db.Where("topic_id = ?", topicId).Order("created_at DESC").Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetById 根据ID获取生成文章记录
func (m *GeneratedArticleModel) GetById(id int64) (*GeneratedArticleRecord, error) {
	var record GeneratedArticleRecord
	if err := m.db.First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// List 获取生成文章记录列表（带分页和条件）
func (m *GeneratedArticleModel) List(page, pageSize int, conditions map[string]interface{}) ([]*GeneratedArticleRecord, int64, error) {
	var records []*GeneratedArticleRecord
	var total int64

	db := m.db.Model(&GeneratedArticleRecord{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "topic_id" || key == "article_id" || key == "llm_model" || key == "status" {
				db = db.Where(key+" = ?", value)
			} else if key == "topic_title" || key == "article_title" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	db = db.Order("created_at DESC")
	if err := db.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
